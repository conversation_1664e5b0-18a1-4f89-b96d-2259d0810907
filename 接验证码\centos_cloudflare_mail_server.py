#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
=== 核心功能 ===
🔄 智能自动化管理
独立后台线程：专门的自动清理线程，不影响主服务性能
定时循环检测：每5分钟自动扫描，无需人工干预
精准时间控制：删除超过5分钟的邮件，保持邮箱清洁
⚡ 高性能并发架构
分层并发设计：
邮件列表读取：单线程（避免API限制）
邮件内容读取：10线程并发
邮件删除操作：10线程并发
批量处理优化：每次处理100封邮件，提高吞吐量
线程池管理：使用 ThreadPoolExecutor 确保资源合理利用
🎯 无缓存设计
实时获取策略：每次都从最新邮件中获取验证码

使用方式
cd / && pkill -f centos_cloudflare_mail_server.py
cd / && sudo nohup python3 centos_cloudflare_mail_server.py > /dev/null 2>&1 &

日志文件：脚本同级目录下的 mail_server.log
日志轮转：每个文件最大10MB，保留3个备份文件
文件列表：mail_server.log, mail_server.log.1, mail_server.log.2, mail_server.log.3
"""

import requests
import logging
import threading
import time
import re
import quopri
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from flask import Flask

# 配置信息
WORKER_DOMAIN = "api.91gmail.cn"
ADMIN_PASSWORD = "yu6709"
DELETE_THRESHOLD_MINUTES = 2  # 删除超过2分钟的邮件
MAX_WORKERS = 10  # 最大并发删除线程数
CLEANUP_INTERVAL_SECONDS = 120  # 清理间隔（2分钟）

# 设置日志 - 输出到脚本同级目录，支持轮转
import os
from logging.handlers import RotatingFileHandler

script_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(script_dir, 'mail_server.log')

# 配置日志轮转：每个文件最大10MB，保留3个备份文件
file_handler = RotatingFileHandler(
    log_file,
    maxBytes=10*1024*1024,  # 10MB
    backupCount=3,          # 保留3个备份文件
    encoding='utf-8'
)

# 配置日志同时输出到文件和控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
    handlers=[
        file_handler,           # 轮转文件输出
        logging.StreamHandler() # 控制台输出
    ]
)
logger = logging.getLogger(__name__)

class AutoMailDeleter:
    """自动邮件删除器 - 删除超过指定时间的邮件"""

    def __init__(self, base_url: str, admin_password: str):
        self.base_url = base_url.rstrip('/')
        self.admin_password = admin_password
        self.session = requests.Session()
        self.session.headers.update({
            'x-admin-auth': admin_password,
            'Content-Type': 'application/json',
            'User-Agent': 'AutoMailDeleter/2.0'
        })
        self.delete_lock = threading.Lock()
        self.stats = {
            'total_checked': 0,
            'total_deleted': 0,
            'last_cleanup': None
        }

    def _make_request(self, method: str, endpoint: str, params: None = None) -> requests.Response:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = self.session.request(method=method, url=url, params=params, timeout=30)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            logger.error(f"API请求失败: {e}")
            raise

    def _parse_time_to_local(self, created_at: str) -> datetime:
        """解析时间并转换为本地时间（UTC+8）"""
        try:
            # 处理ISO格式
            if 'T' in created_at:
                time_str = created_at.replace('Z', '+00:00')
                dt = datetime.fromisoformat(time_str)
                utc_dt = dt.replace(tzinfo=timezone.utc)
                local_dt = utc_dt.astimezone(timezone(timedelta(hours=8)))
                return local_dt.replace(tzinfo=None)
            else:
                # 处理其他格式，假设是UTC时间
                dt = datetime.strptime(created_at, '%Y-%m-%d %H:%M:%S')
                return dt + timedelta(hours=8)
        except Exception as e:
            logger.warning(f"时间解析失败: {created_at}, 错误: {e}")
            return None

    def _is_mail_old(self, created_at: str, threshold_minutes: int) -> bool:
        """检查邮件是否超过指定时间"""
        mail_time = self._parse_time_to_local(created_at)
        if not mail_time:
            return False

        now = datetime.now()
        time_diff = now - mail_time
        return time_diff.total_seconds() > (threshold_minutes * 60)

    def get_mails_batch(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """获取一批邮件数据"""
        params = {'limit': limit, 'offset': offset}
        response = self._make_request('GET', '/admin/mails', params=params)
        return response.json()

    def get_mail_content(self, mail_id: int) -> str:
        """获取指定邮件的内容"""
        try:
            response = self._make_request('GET', f'/admin/mails/{mail_id}')
            data = response.json()
            return data.get('raw', '')
        except Exception as e:
            logger.error(f"获取邮件内容失败: ID {mail_id}, 错误: {e}")
            return ''

    def get_mail_content_batch(self, mail_ids: List[int]) -> Dict[int, str]:
        """批量获取邮件内容（多线程并发）"""
        results = {}

        def get_single_mail_content(mail_id):
            try:
                content = self.get_mail_content(mail_id)
                return mail_id, content
            except Exception as e:
                logger.error(f"获取邮件内容异常: ID {mail_id}, 错误: {e}")
                return mail_id, ''

        # 使用线程池并发读取邮件内容
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_id = {executor.submit(get_single_mail_content, mail_id): mail_id for mail_id in mail_ids}

            for future in as_completed(future_to_id):
                mail_id = future_to_id[future]
                try:
                    mail_id, content = future.result()
                    results[mail_id] = content
                    logger.debug(f"成功获取邮件内容: ID {mail_id}, 长度: {len(content)}")
                except Exception as e:
                    logger.error(f"获取邮件内容异常: ID {mail_id}, 错误: {e}")
                    results[mail_id] = ''

        return results

    def delete_mail(self, mail_id: int) -> bool:
        """删除指定邮件"""
        try:
            response = self._make_request('DELETE', f'/admin/mails/{mail_id}')
            data = response.json()
            return data.get('success', False)
        except Exception as e:
            logger.error(f"删除邮件失败: ID {mail_id}, 错误: {e}")
            return False

    def delete_mail_batch(self, mail_ids: List[int]) -> Dict[str, int]:
        """批量删除邮件（多线程）"""
        results = {'success': 0, 'failed': 0}

        def delete_single_mail(mail_id):
            try:
                if self.delete_mail(mail_id):
                    return 'success'
                else:
                    return 'failed'
            except Exception:
                return 'failed'

        # 使用线程池并发删除
        with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
            future_to_id = {executor.submit(delete_single_mail, mail_id): mail_id for mail_id in mail_ids}

            for future in as_completed(future_to_id):
                mail_id = future_to_id[future]
                try:
                    result = future.result()
                    results[result] += 1
                    if result == 'success':
                        logger.info(f"删除成功: ID {mail_id}")
                    else:
                        logger.warning(f"删除失败: ID {mail_id}")
                except Exception as e:
                    logger.error(f"删除邮件异常: ID {mail_id}, 错误: {e}")
                    results['failed'] += 1

        return results
    
    def auto_delete_old_mails(self, threshold_minutes: int = 5):
        """自动删除超过指定时间的邮件 - 持续删除直到所有符合条件的邮件都被清理"""
        logger.info(f"开始自动删除超过 {threshold_minutes} 分钟的邮件...")

        total_deleted = 0
        total_checked = 0
        batch_size = 100  # API限制，保持100
        round_count = 0

        while True:
            round_count += 1
            round_deleted = 0
            round_checked = 0

            try:
                # 始终从第一页开始获取邮件（因为删除后邮件会重新排序）
                data = self.get_mails_batch(limit=batch_size, offset=0)
                mails = data.get('results', [])
                total_count = data.get('count', 0)

                if not mails:
                    logger.info("没有更多邮件，删除完成")
                    break

                logger.info(f"第 {round_count} 轮检查：获取到 {len(mails)} 封邮件（总计 {total_count} 封）")

                # 收集需要删除的邮件ID
                old_mail_ids = []
                for mail in mails:
                    round_checked += 1
                    mail_id = mail['id']
                    created_at = mail['created_at']

                    # 检查是否需要删除
                    if self._is_mail_old(created_at, threshold_minutes):
                        old_mail_ids.append(mail_id)

                # 如果这一批没有需要删除的邮件，说明剩余邮件都是新的
                if not old_mail_ids:
                    logger.info(f"当前批次没有超过 {threshold_minutes} 分钟的邮件，删除完成")
                    total_checked += round_checked
                    break

                # 批量删除旧邮件
                logger.info(f"第 {round_count} 轮删除：批量删除 {len(old_mail_ids)} 封超过 {threshold_minutes} 分钟的邮件")
                results = self.delete_mail_batch(old_mail_ids)
                round_deleted = results['success']
                total_deleted += round_deleted
                total_checked += round_checked

                if results['failed'] > 0:
                    logger.warning(f"第 {round_count} 轮删除失败: {results['failed']} 封邮件")

                logger.info(f"第 {round_count} 轮完成：检查 {round_checked} 封，删除 {round_deleted} 封")

                # 如果删除的邮件数量少于批次大小，可能已经接近完成
                if round_deleted < batch_size // 2:
                    logger.info("删除数量减少，可能接近完成，继续下一轮...")

            except Exception as e:
                logger.error(f"第 {round_count} 轮处理时出错: {e}")
                break

        # 更新统计信息
        with self.delete_lock:
            self.stats['total_checked'] += total_checked
            self.stats['total_deleted'] += total_deleted
            self.stats['last_cleanup'] = datetime.now()

        logger.info(f"自动删除完成！共 {round_count} 轮，检查了 {total_checked} 封邮件，删除了 {total_deleted} 封邮件")
        return {
            'total_checked': total_checked,
            'total_deleted': total_deleted,
            'rounds': round_count
        }
    def get_stats(self) -> Dict[str, Any]:
        """获取删除统计信息"""
        with self.delete_lock:
            return {
                'total_checked': self.stats['total_checked'],
                'total_deleted': self.stats['total_deleted'],
                'last_cleanup': self.stats['last_cleanup'].strftime('%Y/%m/%d %H:%M:%S') if self.stats['last_cleanup'] else None
            }


# 全局删除器实例
mail_deleter = AutoMailDeleter(
    base_url=f"https://{WORKER_DOMAIN}",
    admin_password=ADMIN_PASSWORD
)


def start_auto_cleanup():
    """启动自动清理后台线程"""
    def cleanup_worker():
        while True:
            try:
                logger.info("开始定时清理超过{DELETE_THRESHOLD_MINUTES}分钟的邮件...")
                result = mail_deleter.auto_delete_old_mails(DELETE_THRESHOLD_MINUTES)
                logger.info(f"定时清理完成: 检查 {result['total_checked']} 封，删除 {result['total_deleted']} 封")

                time.sleep(CLEANUP_INTERVAL_SECONDS)
            except Exception as e:
                logger.error(f"自动清理出错: {e}")
                time.sleep(60)  # 出错时等待1分钟再重试

    cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True, name="AutoCleaner")
    cleanup_thread.start()
    logger.info(f"自动清理后台线程已启动，每 {CLEANUP_INTERVAL_SECONDS//60} 分钟清理一次")
# Flask Web应用
app = Flask(__name__)

# 启动自动清理后台线程
start_auto_cleanup()


@app.route('/api/verification-code', methods=['GET', 'POST'])
def get_verification_code():
    """
    获取验证码API接口

    请求格式:
    POST /api/verification-code
    Content-Type: application/json
    {
        "email": "<EMAIL>"
    }

    或者:
    GET /api/verification-code?email=<EMAIL>

    响应格式:
    成功: 直接返回验证码字符串，如 "123456"
    失败: 返回空字符串 ""
    """
    try:
        from flask import request

        # 获取客户端IP
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR',
                                      request.environ.get('HTTP_X_REAL_IP',
                                      request.remote_addr))

        # 解析请求数据 - 支持GET和POST
        email = None
        if request.method == 'GET':
            # GET请求：从URL参数获取
            email = request.args.get('email')
        elif request.method == 'POST':
            # POST请求：从JSON获取
            if request.is_json:
                data = request.get_json()
                email = data.get('email') if data else None

        if not email:
            return ""

        # 验证邮箱格式
        if not re.match(r'^[^@]+@[^@]+\.[^@]+$', email):
            return ""

        logger.info(f"[{client_ip}] API请求: 获取邮箱 {email} 的验证码")

        try:
            # 获取最近的邮件列表（包含raw内容）
            data = mail_deleter.get_mails_batch(limit=50, offset=0)
            mails = data.get('results', [])

            if not mails:
                logger.info(f"[{client_ip}] 未找到任何邮件")
                return ""

            logger.info(f"[{client_ip}] 获取到 {len(mails)} 封邮件，开始查找验证码...")

            # 直接从邮件列表中查找匹配的邮件和验证码
            for mail in mails:
                raw_content = mail.get('raw', '')

                if not raw_content:
                    continue

                # 查找隐藏邮箱地址（与cloudflare_mail.py相同的逻辑）
                hide_email_match = re.search(r'Hide My Email[^<]*<([^>]+@icloud\.com(?:\.cn)?)>', raw_content)
                if not hide_email_match:
                    continue

                found_hidden_email = hide_email_match.group(1)
                if found_hidden_email.lower() != email.lower():
                    continue

                logger.info(f"[{client_ip}] 找到匹配邮件: {found_hidden_email}")

                # 找到匹配邮件，提取验证码
                verification_code = _extract_code_from_mail(raw_content)

                if verification_code:
                    logger.info(f"[{client_ip}] 成功提取验证码: {verification_code}")

                    # 删除已读邮件
                    mail_id = mail.get('id')
                    if mail_id:
                        try:
                            mail_deleter.delete_mail(mail_id)
                            logger.info(f"[{client_ip}] 已删除邮件: {mail_id}")
                        except Exception as e:
                            logger.warning(f"[{client_ip}] 删除邮件失败: {e}")

                    return verification_code
                else:
                    logger.warning(f"[{client_ip}] 找到匹配邮件但未提取到验证码")

            logger.info(f"[{client_ip}] 未找到发送给 {email} 的邮件")
            return ""

        except Exception as e:
            logger.error(f"[{client_ip}] 获取验证码时出错: {e}")
            return ""

    except Exception as e:
        logger.error(f"API请求处理出错: {e}")
        return ""


def _extract_code_from_mail(raw_content):
    """从邮件原始内容中提取验证码"""
    # 解码QUOTED-PRINTABLE内容
    decoded_content = _decode_mail_content(raw_content)
    if not decoded_content:
        return None

    # 提取纯文本
    clean_text = _extract_clean_text(decoded_content)

    # 查找验证码
    return _find_verification_code(clean_text)


def _decode_mail_content(raw_content):
    """解码邮件内容"""
    qp_pattern = r'Content-Transfer-Encoding:\s*quoted-printable.*?\r?\n(?:.*?\r?\n)*?\r?\n(.*?)(?=\r?\n--|\Z)'
    qp_matches = re.findall(qp_pattern, raw_content, re.IGNORECASE | re.DOTALL)

    decoded_content = ""
    for qp_content in qp_matches:
        try:
            clean_content = qp_content.strip()
            if len(clean_content) > 20:
                decoded_part = quopri.decodestring(clean_content.encode()).decode('utf-8', errors='ignore')
                decoded_part = decoded_part.replace('=\r\n', '').replace('=\n', '')
                decoded_content += decoded_part + "\n"
        except:
            continue

    return decoded_content


def _extract_clean_text(content):
    """提取纯文本，过滤HTML和CSS"""
    lines = content.split('\n')
    clean_lines = []
    in_html_section = False

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 检测HTML开始
        if line.startswith('<!doctype') or line.startswith('<html'):
            in_html_section = True
            continue

        if in_html_section:
            continue

        # 跳过HTML/CSS内容
        if any(keyword in line.lower() for keyword in [
            '<', '>', 'style', 'css', 'html', 'DOCTYPE', 'xmlns',
            'border:', 'margin:', 'padding:', 'background:', 'color:',
            'font-', 'text-', '@media', '.mj-', 'table', 'tbody'
        ]):
            continue

        # 跳过CSS样式行
        if '{' in line or '}' in line or line.endswith(';'):
            continue

        clean_lines.append(line)

    return '\n'.join(clean_lines)


def _find_verification_code(text):
    """在文本中查找验证码"""
    # 优先查找空格分隔的6位数字
    spaced_pattern = re.search(r'(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)', text)
    if spaced_pattern:
        return ''.join(spaced_pattern.groups())

    # 查找常见验证码模式
    patterns = [
        r'(?:code|verification|one-time)\s*(?:is|:)?\s*[:\s]*(\d{6})',
        r'(\d{6})\s*(?:is your)?\s*(?:code|verification)',
        r'Your\s+(?:verification\s+)?code\s*[:\s]*(\d{6})'
    ]

    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1)

    # 查找独立的6位数字
    lines = text.split('\n')
    for line in lines:
        line = line.strip()
        if any(keyword in line.lower() for keyword in ['<', '>', 'style', 'css', 'html']):
            continue
        simple_match = re.search(r'\b(\d{6})\b', line)
        if simple_match:
            return simple_match.group(1)

    return None


# API配置
API_CONFIG = {
    "host": "0.0.0.0",  # 监听所有网络接口
    "port": 8603,       # 默认端口
    "debug": False,     # 生产环境关闭调试
    "threaded": True    # 启用多线程
}

if __name__ == "__main__":
    print("=" * 60)
    print(f"监听地址: {API_CONFIG['host']}:{API_CONFIG['port']}")
    print("API接口:")
    print("  获取验证码: POST /api/verification-code")

    app.run(
        host=API_CONFIG['host'],
        port=API_CONFIG['port'],
        debug=API_CONFIG['debug'],
        threaded=API_CONFIG['threaded']
    )
