# 🚀 Cursor自动注册工具 - 精准打包指南

## 📋 概述

本项目现在使用 `.spec` 文件进行精准打包，相比直接使用 PyInstaller 命令行参数，具有更好的可控性和可维护性。

## 🎯 打包方式

### 方式一：使用批处理脚本（推荐）

```bash
# Windows
build_spec.bat
```

### 方式二：使用 Python 脚本

```bash
python build.py
```

### 方式三：直接使用 PyInstaller

```bash
pyinstaller Cursor自动注册.spec --clean
```

## 📁 文件结构

```
mail_cursor/
├── Cursor自动注册.py          # 主程序
├── Cursor自动注册.spec        # PyInstaller 配置文件
├── build_spec.bat             # Windows 打包脚本
├── build.py                   # Python 打包脚本
├── BUILD_README.md            # 本文件
├── turnstilePatch/            # Turnstile 补丁
├── 注册相关/                  # 注册相关模块
├── 接验证码/                  # 验证码相关模块
├── build/                     # 构建临时文件（被git忽略）
└── dist/                      # 输出目录
    └── Cursor自动注册工具.exe # 最终可执行文件
```

## ⚙️ .spec 文件配置说明

### 数据文件包含
```python
datas=[
    ('turnstilePatch', 'turnstilePatch'),
    ('注册相关', '注册相关'),
    ('接验证码', '接验证码'),
]
```

### 隐藏导入
自动包含所有必要的 Python 模块，包括：
- tkinter 相关模块
- selenium 相关模块
- requests, logging 等

### 排除模块
排除不必要的大型库：
- matplotlib, numpy, pandas
- tensorflow, torch
- jupyter, IPython

### 优化选项
- `optimize=2`: 最高级别的字节码优化
- `strip=True`: 去除调试符号
- `upx=True`: 启用 UPX 压缩

## 🎨 自定义配置

### 添加图标
在 `.spec` 文件中修改：
```python
icon='path/to/your/icon.ico'
```

### 添加版本信息
```python
version_info='path/to/version_info.txt'
```

### 修改输出文件名
```python
name='你的程序名'
```

## 🔧 故障排除

### 常见问题

1. **编码错误**
   - 确保所有文件使用 UTF-8 编码
   - 在 Windows 上使用 `chcp 65001` 设置控制台编码

2. **模块缺失**
   - 检查 `hiddenimports` 列表
   - 添加缺失的模块到 `.spec` 文件

3. **文件过大**
   - 检查 `excludes` 列表
   - 添加不需要的模块到排除列表

4. **运行时错误**
   - 检查 `datas` 配置
   - 确保所有资源文件都被正确包含

## 📊 性能对比

| 方式 | 文件大小 | 启动速度 | 可维护性 |
|------|----------|----------|----------|
| 命令行 | 较大 | 一般 | 低 |
| .spec文件 | 较小 | 快 | 高 |

## 🎉 优势

1. **精准控制**: 可以精确控制包含和排除的模块
2. **可重复构建**: 配置文件化，确保每次构建结果一致
3. **易于维护**: 所有配置集中在 `.spec` 文件中
4. **更小体积**: 通过排除不必要的模块减小文件大小
5. **更快启动**: 优化配置提升程序启动速度

## 📝 注意事项

1. 修改 `.spec` 文件后需要重新打包
2. `build/` 目录会被 git 忽略，`dist/` 目录会被跟踪
3. 建议在打包前清理 `build/` 和 `__pycache__/` 目录
4. 首次打包可能需要较长时间，后续打包会更快

## 🚀 快速开始

1. 确保安装了 PyInstaller: `pip install pyinstaller`
2. 运行打包脚本: `build_spec.bat`
3. 在 `dist/` 目录找到生成的可执行文件

---

*最后更新: 2025-07-26*
