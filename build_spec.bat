@echo off
chcp 65001 >nul
echo ============================================================
echo 🚀 Cursor自动注册工具 - .spec 精准打包
echo ============================================================

echo.
echo 🧹 清理构建目录...
if exist build rmdir /s /q build
if exist __pycache__ rmdir /s /q __pycache__
echo    ✅ 清理完成

echo.
echo 🔨 开始打包...
pyinstaller Cursor自动注册.spec --clean

if %errorlevel% equ 0 (
    echo.
    echo ✅ 打包成功!
    echo 📁 可执行文件位置: dist\Cursor自动注册工具.exe
    
    echo.
    echo 📊 文件信息:
    for %%f in (dist\Cursor自动注册工具.exe) do (
        echo    文件名: %%~nxf
        echo    大小: %%~zf 字节
    )
) else (
    echo.
    echo ❌ 打包失败!
)

echo.
echo ============================================================
pause
