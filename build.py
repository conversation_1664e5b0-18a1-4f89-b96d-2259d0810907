#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cursor自动注册工具打包脚本
使用 .spec 文件进行精准打包
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import time

def clean_build_dirs():
    """清理构建目录"""
    print("🧹 清理构建目录...")
    
    dirs_to_clean = ['build', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"   ✅ 已删除: {dir_name}/")
            except Exception as e:
                print(f"   ⚠️  删除 {dir_name}/ 失败: {e}")
    
    # 清理 .pyc 文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                except:
                    pass

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_files = [
        'Cursor自动注册.py',
        'Cursor自动注册.spec',
        'turnstilePatch',
        '注册相关',
        '接验证码',
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("   ✅ 所有必要文件都存在")
    return True

def build_executable():
    """使用 .spec 文件构建可执行文件"""
    print("🔨 开始构建可执行文件...")
    
    try:
        # 使用 .spec 文件进行打包
        cmd = [sys.executable, '-m', 'PyInstaller', 'Cursor自动注册.spec', '--clean']
        
        print(f"   执行命令: {' '.join(cmd)}")
        
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        end_time = time.time()
        
        if result.returncode == 0:
            print(f"   ✅ 构建成功! 耗时: {end_time - start_time:.1f}秒")
            return True
        else:
            print("❌ 构建失败!")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中出错: {e}")
        return False

def check_output():
    """检查输出文件"""
    print("📦 检查输出文件...")
    
    exe_path = Path('dist/Cursor自动注册工具.exe')
    
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"   ✅ 可执行文件已生成: {exe_path}")
        print(f"   📏 文件大小: {file_size:.1f} MB")
        return True
    else:
        print(f"   ❌ 未找到可执行文件: {exe_path}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 Cursor自动注册工具 - 精准打包脚本")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists('Cursor自动注册.py'):
        print("❌ 请在项目根目录运行此脚本!")
        sys.exit(1)
    
    # 步骤1: 清理构建目录
    clean_build_dirs()
    print()
    
    # 步骤2: 检查依赖
    if not check_dependencies():
        sys.exit(1)
    print()
    
    # 步骤3: 构建可执行文件
    if not build_executable():
        sys.exit(1)
    print()
    
    # 步骤4: 检查输出
    if not check_output():
        sys.exit(1)
    print()
    
    print("🎉 打包完成!")
    print("📁 可执行文件位置: dist/Cursor自动注册工具.exe")
    print("=" * 60)

if __name__ == '__main__':
    main()
